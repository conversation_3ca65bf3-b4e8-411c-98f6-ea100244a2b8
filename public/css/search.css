/* 搜索框样式 */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box {
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  width: 0;
  opacity: 0;
  transition: width 0.3s ease, opacity 0.3s ease;
  z-index: 100;
}

.search-box.active {
  width: 450px;
  opacity: 1;
}

.search-box input {
  flex: 1;
  border: none;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
}

.search-box button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}
