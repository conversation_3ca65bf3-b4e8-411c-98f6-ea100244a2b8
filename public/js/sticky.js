/**
 * 处理粘性元素的滚动行为
 */

// 增强的跟随页面滚动功能，解决滚动到底部的问题和布局混乱问题
document.addEventListener('DOMContentLoaded', function() {
  // 处理事件容器
  handleStickyElement('events-container', 'events-sticky', 'events-placeholder');
  
  function handleStickyElement(containerId, elementId, placeholderId) {
    const container = document.getElementById(containerId);
    const stickyElement = document.getElementById(elementId);
    const placeholder = document.getElementById(placeholderId);
    
    if (!container || !stickyElement || !placeholder) return;
    
    // 设置占位符的初始高度和宽度，与粘性元素相同
    placeholder.style.height = stickyElement.offsetHeight + 'px';
    placeholder.style.width = stickyElement.offsetWidth + 'px';
    
    // 计算容器的位置信息
    let containerRect = container.getBoundingClientRect();
    let containerTop = containerRect.top + window.pageYOffset;
    let containerBottom = containerRect.bottom + window.pageYOffset;
    let containerHeight = containerRect.height;
    let stickyHeight = stickyElement.offsetHeight;
    
    // 监听窗口大小变化，更新位置信息
    window.addEventListener('resize', function() {
      containerRect = container.getBoundingClientRect();
      containerTop = containerRect.top + window.pageYOffset;
      containerBottom = containerRect.bottom + window.pageYOffset;
      containerHeight = containerRect.height;
      stickyHeight = stickyElement.offsetHeight;
      
      // 更新占位符尺寸
      placeholder.style.height = stickyElement.offsetHeight + 'px';
      placeholder.style.width = stickyElement.offsetWidth + 'px';
      
      // 触发滚动事件以更新位置
      window.dispatchEvent(new Event('scroll'));
    });
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;
      
      // 计算元素应该处于的状态
      if (scrollTop < containerTop - 20) {
        // 1. 还没滚动到容器顶部，保持正常流
        stickyElement.style.position = '';  // 清除position属性，使用CSS默认值
        stickyElement.style.top = '';
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '';  // 清除z-index，使用CSS默认值
        placeholder.classList.remove('show-placeholder');
      }
      else if (scrollTop + stickyHeight + 20 >= containerBottom) {
        // 2. 滚动到容器底部，固定在容器底部
        stickyElement.style.position = 'absolute';
        stickyElement.style.top = '0';  // 设置为0，因为已经在CSS中设置了margin-top: 20px
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
      else {
        // 3. 在容器中间滚动，固定在视口顶部
        stickyElement.style.position = 'fixed';
        stickyElement.style.top = '20px';  // 保持20px的顶部间距
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
    });
    
    // 初始触发一次滚动事件
    window.dispatchEvent(new Event('scroll'));
  }
});
