/**
 * 通用JavaScript函数
 */

// 轮播图初始化和控制
function initCarousel() {
  const slides = document.querySelectorAll('.carousel-slides img');
  const indicators = document.querySelector('.carousel-indicators');
  
  if (!slides.length || !indicators) return;
  
  let currentSlide = 0;

  slides.forEach((_, index) => {
    const indicator = document.createElement('div');
    indicator.className = `indicator ${index === 0 ? 'active' : ''}`;
    indicator.textContent = index + 1;
    indicator.onclick = () => goToSlide(index);
    indicators.appendChild(indicator);
  });

  function goToSlide(index) {
    slides.forEach((slide, i) => {
      slide.style.display = i === index ? 'block' : 'none';
    });
    document.querySelectorAll('.indicator').forEach((ind, i) => {
      ind.className = `indicator ${i === index ? 'active' : ''}`;
    });
    currentSlide = index;
  }

  // 自动轮播
  setInterval(() => {
    currentSlide = (currentSlide + 1) % slides.length;
    goToSlide(currentSlide);
  }, 3000);

  goToSlide(0);
}

// 表格行悬停效果
function initTableHover() {
  const tableRows = document.querySelectorAll('.listTable tr');

  tableRows.forEach(row => {
    row.addEventListener('mouseenter', () => {
      row.classList.add('bg');
    });

    row.addEventListener('mouseleave', () => {
      row.classList.remove('bg');
    });
  });
}

// 元素轮换显示
function setupRotation(selector) {
  const elements = document.querySelectorAll(selector + ' a');
  
  // 只在找到元素时继续
  if (elements.length === 0) {
    return null; // 如果没有找到元素则返回null
  }

  let currentIndex = 0;

  for (let i = 0; i < elements.length; i++) {
    if (i > 0) {
      elements[i].style.display = 'none';
    }
  }

  function rotateNext() {
    elements[currentIndex].style.display = 'none';
    currentIndex = (currentIndex + 1) % elements.length;
    elements[currentIndex].style.display = 'block';
  }

  rotateNext();
  return setInterval(rotateNext, 2500);
}

// 初始化轮换显示
function initRotations() {
  // 只在元素存在时调用setupRotation
  const float1Elements = document.querySelector('.float1');
  const float2Elements = document.querySelector('.float2');
  const secondaryBanner = document.querySelector('.secondary-banner');

  if (float1Elements) setupRotation('.float1');
  if (float2Elements) setupRotation('.float2');
  if (secondaryBanner) setupRotation('.secondary-banner');
}

// JSONP回调函数
function jsonpCallback(data) {
  var ads = getByClass('cd-ads');
  if (!data) {
    for (var i = 0; i < ads.length; i++) {
      removeClass(ads[i], 'cd-ads');
    }
  }
}

// 获取JSONP数据
function get_jsonp() {
  var JSONP = document.createElement("script");
  JSONP.id = "jsonp_script";
  JSONP.type = "text/javascript";
  JSONP.src = "https://tool.chasedream.com/iptools/ip/ise?callback=jsonpCallback";
  document.getElementsByTagName("head")[0].appendChild(JSONP);
}

// 通过类名获取元素
function getByClass(sClass, parent) {
  var aEles = (parent || document).getElementsByTagName('*');
  var arr = [];
  for (var i = 0; i < aEles.length; i++) {
    var aClass = aEles[i].className.split(' ');
    for (var j = 0; j < aClass.length; j++) {
      if (aClass[j] == sClass) {
        arr.push(aEles[i]);
        break;
      }
    }
  }
  return arr;
}

// 移除类名
function removeClass(obj, sClass) {
  var aClass = obj.className.split(' ');
  if (!aClass[0]) return;
  for (var i = 0; i < aClass.length; i++) {
    if (aClass[i] == sClass) {
      aClass.splice(i, 1);
      obj.className = aClass.join(' ');
      return;
    }
  }
}

// 广告处理
var addCss = {
  init: function () {
    if (this.getClass('cdg-content').length < 1) {
      return false;
    }
    this.creatAdTx(this.getClass('w1'));
    this.creatAdTx(this.getClass('w2'));
    this.creatAdTx(this.getClass('w3'));
    this.creatAdTx(this.getClass('w4'));
    this.creatAdTx(this.getClass('b1'));
    this.creatAdTx(this.getClass('b2'));
    this.creatAdTx(this.getClass('b3'));
    this.creatAdTx(this.getClass('b4'));
  },
  creatAdTx: function (ads) {
    var adText = '<span>广告</span>'
    if (ads.length < 1) {
      return false;
    }
    for (var i = 0; i < ads.length; i++) {
      ads[i].innerHTML = ads[i].innerHTML + adText;
    }
  },
  getClass: function getByClass(sClass, parent) {
    var aEles = (parent || document).getElementsByTagName('*');
    var arr = [];
    for (var i = 0; i < aEles.length; i++) {
      var aClass = aEles[i].className.split(' ');
      for (var j = 0; j < aClass.length; j++) {
        if (aClass[j] == sClass) {
          arr.push(aEles[i]);
          break;
        }
      }
    }
    return arr;
  }
}

// 页面初始化
function init() {
  initCarousel();
  initTableHover();
  initRotations();
  get_jsonp();
  
  // 页面加载完成后初始化广告
  window.addEventListener('load', (event) => {
    addCss.init();
  });
}

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', init);
