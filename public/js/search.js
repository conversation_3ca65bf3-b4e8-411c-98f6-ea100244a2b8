document.addEventListener('DOMContentLoaded', function() {
  // 获取搜索图标和搜索框元素
  const searchIcon = document.querySelector('.search-icon');
  const searchBox = document.querySelector('.search-box');
  const searchInput = searchBox.querySelector('input');
  const searchForm = document.querySelector('.search-box');

  // 点击搜索图标时切换搜索框的显示状态
  searchIcon.addEventListener('click', function(e) {
    e.preventDefault();
    searchBox.classList.toggle('active');
    
    // 如果搜索框变为可见，则自动聚焦到输入框
    if (searchBox.classList.contains('active')) {
      setTimeout(() => {
        searchInput.focus();
      }, 300); // 等待动画完成后聚焦
    }
  });

  // 点击页面其他地方时关闭搜索框
  document.addEventListener('click', function(e) {
    if (!searchBox.contains(e.target) && !searchIcon.contains(e.target)) {
      searchBox.classList.remove('active');
    }
  });

  // 处理搜索表单提交
  searchForm.addEventListener('submit', function(e) {
    e.preventDefault();
    const query = searchInput.value.trim();
    if (query) {
      window.location.href = '/search?q=' + encodeURIComponent(query);
    }
  });

  // 监听搜索框中的回车键
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      const query = searchInput.value.trim();
      if (query) {
        window.location.href = '/search?q=' + encodeURIComponent(query);
      }
    }
  });
});
