/**
 * 处理分页功能
 */

// 初始化分页
function initPagination(options) {
  const defaults = {
    currentPage: 1,
    totalPage: 1,
    count: 7,
    isShow: true,
    homePageText: "首页",
    endPageText: "尾页",
    prevPageText: "上一页",
    nextPageText: "下一页",
    callback: null,
    container: '#pagination'
  };

  const settings = Object.assign({}, defaults, options);
  
  $(settings.container).pagination({
    currentPage: settings.currentPage,
    totalPage: settings.totalPage,
    count: settings.count,
    isShow: settings.isShow,
    homePageText: settings.homePageText,
    endPageText: settings.endPageText,
    prevPageText: settings.prevPageText,
    nextPageText: settings.nextPageText,
    callback: function(current) {
      if (current !== settings.currentPage && typeof settings.callback === 'function') {
        settings.callback(current);
      }
    }
  });
}

// 搜索页面分页初始化
function initSearchPagination(totalItems, currentPage, totalPages) {
  const count = Math.min(7, totalPages);
  
  initPagination({
    currentPage: currentPage,
    totalPage: totalPages,
    count: count,
    callback: function(current) {
      const searchParams = new URLSearchParams(window.location.search);
      const q = searchParams.get('q') || '';
      window.location.href = `/search?page=${current}&q=${encodeURIComponent(q)}`;
    }
  });
  
  // 添加表单提交处理程序
  const searchForm = document.getElementById('searchForm');
  if (searchForm) {
    searchForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const q = document.getElementById('searchInput').value.trim();
      if (q) {
        window.location.href = `/search?q=${encodeURIComponent(q)}`;
      }
    });
  }
}

// 分类页面分页初始化
function initCategoryPagination(totalItems, currentPage, totalPages, category) {
  const count = Math.min(7, totalPages);
  
  initPagination({
    currentPage: currentPage,
    totalPage: totalPages,
    count: count,
    callback: function(current) {
      window.location.href = `/category/${category}/${current}`;
    }
  });
}
