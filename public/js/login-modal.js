document.addEventListener('DOMContentLoaded', function() {
  // 获取登录按钮和弹出框元素
  const loginButton = document.getElementById('login-button');
  const loginModalOverlay = document.getElementById('login-modal-overlay');
  const loginModalClose = document.getElementById('login-modal-close');
  const loginIframe = document.querySelector('.login-modal-iframe');
  
  // 点击登录按钮时显示弹出框
  if (loginButton) {
    loginButton.addEventListener('click', function(e) {
      e.preventDefault();
      if (loginModalOverlay) {
        loginModalOverlay.classList.add('active');
      }
    });
  }
  
  // 点击关闭按钮时隐藏弹出框
  if (loginModalClose) {
    loginModalClose.addEventListener('click', function() {
      if (loginModalOverlay) {
        loginModalOverlay.classList.remove('active');
      }
    });
  }
  
  // 点击弹出框外部区域时隐藏弹出框
  if (loginModalOverlay) {
    loginModalOverlay.addEventListener('click', function(e) {
      if (e.target === loginModalOverlay) {
        loginModalOverlay.classList.remove('active');
      }
    });
  }
  
  // 按ESC键时隐藏弹出框
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && loginModalOverlay && loginModalOverlay.classList.contains('active')) {
      loginModalOverlay.classList.remove('active');
    }
  });
  
  // 监听来自iframe的消息
  window.addEventListener('message', function(event) {
    // 输出所有收到的消息，用于调试
    console.log('收到消息:', event);
    console.log('消息来源:', event.origin);
    console.log('消息数据:', event.data);
    
    // 确保消息来自我们信任的域名
    if (event.origin.includes('id.chasedream.com') || event.origin.includes('chasedream.com')) {
      try {
        let data;
        try {
          // 尝试解析JSON字符串
          data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        } catch (e) {
          // 如果解析失败，直接使用原始数据
          data = event.data;
        }
        
        // 输出登录结果到控制台
        console.log('处理后的登录结果:', data);
        
        // 检查是否登录成功 - 使用更宽松的条件
        if (data.register_type === 'login') {
          console.log('检测到登录成功，即将跳转到论坛页面');
          // 登录成功，跳转到论坛页面
          setTimeout(function() {
            window.location.href = 'https://forum.chasedream.com';
          }, 500); // 添加延迟，确保消息被完全处理
        } else if (data && (data.code === 400 || 
            (typeof data === 'string' && data.includes('error')))) {
          console.log('检测到登录失败:', data.msg || data);          
        }
      } catch (error) {
        console.error('处理消息时出错:', error);
      }
    }
  });
  
  // 为iframe添加load事件监听器
  if (loginIframe) {
    loginIframe.addEventListener('load', function() {
      // 尝试向iframe发送消息，建立通信
      try {
        loginIframe.contentWindow.postMessage('ready', 'https://id.chasedream.com');
      } catch (error) {
        console.log('向iframe发送消息失败，可能是跨域限制:', error);
      }
    });
  }
});
