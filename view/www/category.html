<html>
<%- include('./partials/header-category') %>

<head>
  <link rel="stylesheet" href="/public/css/sticky.css" type="text/css" />
  <!-- 引入jQuery和分页插件 -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <link href="/public/js/pagination/jquery.pagination.css" rel="stylesheet">
  <script src="/public/js/pagination/jquery.pagination.min.js"></script>
</head>

<body>
  <div class="main-content">
    <%- include('./partials/events') %>
    <div class="content-area">
      <% articles.forEach(function(article, index) { %>
            <div class="news-item">
              <% if (article.type === 1) { %>
                <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>" target="_blank">
                  <img src="<%= article.pic %>" width="600" height="140" alt="<%= article.title %>" />
                </a>
              <% } else { %>
                <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>">
                  <img class="news-image" src="https://mat.chasedream.com/chasedream/test/images/400x240/<%= article.idx%>.png" />
                  <div class="news-content">
                    <h3><%= article.title %></h3>
                    <span class="news-subtitle"><%= article.summary %></span>
                    <!--div class="news-date">
                      <%= timestampToDate(article.datetime, 'MM-DD') %>
                    </div-->
                  </div>
                </a>
              <% } %>
            </div>
          <% }); %>
    </div>
    <div class="main-right">
      <img src="https://mat.chasedream.com/chasedream/test/images/ad6.png" width="260" height="140" />
      <img src="https://mat.chasedream.com/chasedream/test/images/ad6.png" width="260" height="140" />
    </div>
  </div>
  
  <div id="pagination" class="pagination"></div>
  
  <%- include('./partials/footer') %>
  
  <!-- 登录弹出框 -->
  <div class="login-modal-overlay" id="login-modal-overlay">
    <div class="login-modal">
      <svg class="login-modal-close" id="login-modal-close" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="width: 24px; height: 24px;"><g fill="none"><path d="M4.5 4.5L19.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19.5 4.5L4.5 19.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>        
      <iframe class="login-modal-iframe" src="<%= idUrl%>/login-www-pc?t=<%= Math.random() %>" frameborder="0"></iframe>
    </div>
  </div>
  
  <!-- 引入公共JavaScript文件 -->
  <script src="/public/js/common.js"></script>
  <script src="/public/js/login-modal.js"></script>
  
  <script>
    // 将分页数据存储在全局变量中，避免在JavaScript中使用EJS表达式
    var paginationData = {
      current: <%= pagination.current %>,
      total: <%= pagination.total %>,
      totalItems: <%= pagination.totalItems %>,
      category: '<%= category %>'
    };
    
    // 页面加载完成后初始化分页
    $(document).ready(function() {
      $('#pagination').pagination({
        currentPage: paginationData.current,
        totalPage: paginationData.total,
        count: Math.min(7, paginationData.total),
        isShow: true,
        homePageText: "首页",
        endPageText: "尾页",
        prevPageText: "上一页",
        nextPageText: "下一页",
        callback: function(current) {
          if (current !== paginationData.current) {
            window.location.href = `/category/${paginationData.category}/${current}`;
          }
        }
      });
    });
  </script>
</body>
</html>
