<html>
<%- include('./partials/header-category') %>

<head>
  <link rel="stylesheet" href="/public/css/search.css" type="text/css" />
  <link rel="stylesheet" href="/public/css/sticky.css" type="text/css" />
  <!-- 引入jQuery和分页插件 -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <link href="/public/js/pagination/jquery.pagination.css" rel="stylesheet">
  <script src="/public/js/pagination/jquery.pagination.min.js"></script>
  <style>
    .search-form {
      display: flex;
      gap: 10px;
    }
    
    #searchInput {
      flex: 1;
      padding: 10px;
      font-size: 16px;
      border: 2px solid #ddd;
      border-radius: 4px;
    }
    
    .search-form button {
      padding: 10px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    
    .search-form button:hover {
      background-color: #0056b3;
    }

    .highlight em {
      background-color: #FFE082;
      color: #000;
      font-style: normal;
      padding: 0 2px;
      border-radius: 2px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      transition: background-color 0.2s ease;
    }

    .highlight em:hover {
      background-color: #FFD54F;
    }
  </style>
</head>

<body>
  <div class="main-content">
    <%- include('./partials/events') %>
    <div class="content-area">
      <div class="search-container">
      </div>
      
      <% articles.forEach(function(article, index) { %>
        <div class="news-item">
          <% if (article.type === 1) { %>
            <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>" target="_blank">
              <img src="<%= article.pic %>" width="600" height="140" alt="<%= article.title %>" />
            </a>
          <% } else { %>
            <a href="<%= article.externalLink ? article.externalLink : '/article/' + article.id %>">
              <img class="news-image" src="https://mat.chasedream.com/chasedream/test/images/400x240/<%= article.idx%>.png" />
              <div class="news-content">
                <h3><%- article.title %></h3>
                <span class="news-subtitle"><%- article.summary %></span>
                <!--div class="news-date">
                  <%= timestampToDate(article.datetime, 'MM-DD') %>
                </div-->
              </div>
            </a>
          <% } %>
        </div>
      <% }); %>
    </div>
    <div class="main-right">
      <img src="https://mat.chasedream.com/chasedream/test/images/ad6.png" width="220" height="118" />
      <img src="https://mat.chasedream.com/chasedream/test/images/ad6.png" width="220" height="118" />
    </div>
  </div>
  
  <div id="pagination" class="pagination"></div>
  
  <%- include('./partials/footer') %>
  
  <!-- 引入公共JavaScript文件 -->
  <script src="/public/js/common.js"></script>
  
  <script>
    // 将分页数据存储在全局变量中，避免在JavaScript中使用EJS表达式
    var paginationData = {
      current: <%= pagination.current %>,
      total: <%= pagination.total %>,
      totalItems: <%= pagination.totalItems %>
    };
    
    // 页面加载完成后初始化分页
    $(document).ready(function() {
      $('#pagination').pagination({
        currentPage: paginationData.current,
        totalPage: paginationData.total,
        count: Math.min(7, paginationData.total),
        isShow: true,
        homePageText: "首页",
        endPageText: "尾页",
        prevPageText: "上一页",
        nextPageText: "下一页",
        callback: function(current) {
          if (current !== paginationData.current) {
            const searchParams = new URLSearchParams(window.location.search);
            const q = searchParams.get('q') || '';
            window.location.href = `/search?page=${current}&q=${encodeURIComponent(q)}`;
          }
        }
      });

      // 添加表单提交处理程序
      const searchForm = document.getElementById('searchForm');
      if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
          e.preventDefault();
          const q = document.getElementById('searchInput').value.trim();
          if (q) {
            window.location.href = `/search?q=${encodeURIComponent(q)}`;
          }
        });
      }
    });
  </script>
</body>
</html>
