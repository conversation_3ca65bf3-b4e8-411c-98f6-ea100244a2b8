import { CcCommonSearchRecommendGroup } from './../../entity/forum/CcCommonSearchRecommendGroup';
import { CcCommonSearchRecommend } from './../../entity/forum/CcCommonSearchRecommend';
import {
  Config,
  Inject,
  Provide,
  Task,
  FORMAT,
  Logger,
  App,
} from '@midwayjs/decorator';
import { BaseService, CoolCache } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { CoolElasticSearch } from '@cool-midway/es';
import { ILogger } from '@midwayjs/logger';
import * as _ from 'lodash';
import * as phpunserialize from 'phpunserialize';
import { CcForumThread } from '../../entity/forum/CcForumThread';
import { CcForumPost } from '../../entity/forum/CcForumPost';
import { BaseSysConfService } from '../sys/conf';
import { CcForumThreadTrigger } from '../../entity/forum/CcForumThreadTrigger';
import { CcForumPostTrigger } from '../../entity/forum/CcForumPostTrigger';
import { CcCommonAdvertisement } from '../../entity/forum/CcCommonAdvertisement';
import { CcForumThreadWeight } from '../../entity/forum/CcForumThreadWeight';
import { CcCommonMember } from '../../entity/forum/CcCommonMember';
import { EmailService } from './email';
import { Application } from '@midwayjs/koa';
import { CommonSensitiveEntity } from '../../entity/common/sensitive';
import { CcCommonMemberCrime } from '../../entity/forum/CcCommonMemberCrime';
import { CcCommonMemberFieldForum } from '../../entity/forum/CcCommonMemberFieldForum';
import { CcCommonMemberProfile } from '../../entity/forum/CcCommonMemberProfile';
import { CcHomeNotification } from '../../entity/forum/CcHomeNotification';
import { CcForumPostTableid } from '../../entity/forum/CcForumPostTableid';
import { CcForumThreadpartake } from '../../entity/forum/CcForumThreadpartake';
import { TblEvent } from '../../entity/event/TblEvent';
import { IwmsClass } from '../../entity/www/IwmsClass';
import { IwmsNews } from '../../entity/www/IwmsNews';
import { encode } from 'html-entities';
import axios from 'axios';
import { TblEventUser } from '../../entity/event/TblEventUser';
import { TblSystemSettings } from '../../entity/event/TblSystemSettings';

/**
 * 论坛
 */
@Provide()
export class CommonForumService extends BaseService {
  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @InjectEntityModel(CcForumThread, 'forum')
  ccForumThread: Repository<CcForumThread>;

  @InjectEntityModel(CcForumPost, 'forum')
  ccForumPost: Repository<CcForumPost>;

  @InjectEntityModel(CcForumThreadpartake, 'forum')
  ccForumThreadpartake: Repository<CcForumThreadpartake>;

  @InjectEntityModel(CcForumPostTableid, 'forum')
  ccForumPostTableid: Repository<CcForumPostTableid>;

  @InjectEntityModel(CcCommonSearchRecommend, 'forum')
  ccCommonSearchRecommend: Repository<CcCommonSearchRecommend>;

  @InjectEntityModel(CcCommonSearchRecommendGroup, 'forum')
  ccCommonSearchRecommendGroup: Repository<CcCommonSearchRecommendGroup>;

  @InjectEntityModel(CcForumThreadTrigger, 'forum')
  CcForumThreadTrigger: Repository<CcForumThreadTrigger>;

  @InjectEntityModel(CcForumPostTrigger, 'forum')
  CcForumPostTrigger: Repository<CcForumPostTrigger>;

  @InjectEntityModel(CcCommonAdvertisement, 'forum')
  ccCommonAdvertisement: Repository<CcCommonAdvertisement>;

  @InjectEntityModel(CcForumThreadWeight, 'forum')
  ccForumThreadWeight: Repository<CcForumThreadWeight>;

  @InjectEntityModel(CcCommonMemberCrime, 'forum')
  ccCommonMemberCrime: Repository<CcCommonMemberCrime>;

  @InjectEntityModel(CcCommonMemberProfile, 'forum')
  ccCommonMemberProfile: Repository<CcCommonMemberProfile>;

  @InjectEntityModel(CcCommonMemberFieldForum, 'forum')
  ccCommonMemberFieldForum: Repository<CcCommonMemberFieldForum>;

  @InjectEntityModel(CcHomeNotification, 'forum')
  ccHomeNotification: Repository<CcHomeNotification>;

  @InjectEntityModel(CommonSensitiveEntity)
  commonSensitiveEntity: Repository<CommonSensitiveEntity>;

  @InjectEntityModel(TblEvent, 'id')
  tblEvent: Repository<TblEvent>;

  @InjectEntityModel(TblEventUser, 'id')
  tblEventUser: Repository<TblEventUser>;

  @InjectEntityModel(TblSystemSettings, 'id')
  tblSystemSettings: Repository<TblSystemSettings>;

  @InjectEntityModel(IwmsClass, 'www')
  iwmsClass: Repository<IwmsClass>;

  @InjectEntityModel(IwmsNews, 'www')
  iwmsNews: Repository<IwmsNews>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  emailService: EmailService;

  @Logger()
  logger: ILogger;

  @App()
  app: Application;

  @Inject()
  es: CoolElasticSearch;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  @Config('elasticSearch')
  elasticSearch;

  @Config('discuz')
  discuz;

  async updateShowMobile(param: any) {
    await this.tblSystemSettings.update(
      { name: 'show_mobile' },
      {
        value: param.showMobile,
      }
    );
  }

  async getShowMobile(param: any) {
    return await this.tblSystemSettings.findOne({
      where: {
        name: 'show_mobile',
      },
    });
  }

  async appFeatureSwitch(param: any) {
    await this.baseSysConfService.updateVaule(
      'appFeatureSwitch',
      JSON.stringify(param)
    );
  }

  async deleteThread(body) {
    const res = await this.loginForum(body);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    body.formhash = variables.formhash;

    await axios.post(
      this.discuz.deleteThread,
      {
        fid: body.fid,
        formhash: body.formhash,
        handlekey: 'mods',
        listextra: 'page%3D1',
        'moderate[]': body.tid,
        'operations[]': 'delete',
      },
      {
        maxRedirects: 0,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );
  }

  async clearForumCache(cookie) {
    await axios.get(this.discuz.cache),
      {
        headers: {
          Cookie: cookie,
        },
      };
  }

  async stick(param: any) {
    const obj: any = {};
    const event = await this.tblEvent.findOne({
      where: {
        id: param.eventId,
      },
    });

    const user = await this.tblEventUser.findOne({
      where: {
        id: '2',
      },
    });

    obj.stick = param.stick;
    obj.username = user.username;
    obj.password = this.utils.decodeDZString(user.password);

    obj.fid = event.forum_url_fid;
    obj.tid = event.forum_url;

    const res = await this.loginForum(obj);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    obj.formhash = variables.formhash;

    await axios.post(
      `${this.discuz.stick}`,
      {
        formhash: obj.formhash,
        fid: obj.fid,
        listextra: 'page%3D1',
        handlekey: 'mods',
        'moderate[]': obj.tid,
        'operations[]': 'stick',
        sticklevel: obj.stick,
      },
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );

    await this.tblEvent.update(
      {
        id: param.eventId,
      },
      {
        stick: obj.stick,
      }
    );

    await this.clearForumCache(cookies);
  }

  async highlightDigest(param: any) {
    const obj: any = {};
    const event: any = await this.tblEvent.findOneBy({
      id: param.eventId,
    });

    const user = await this.tblEventUser.findOne({
      where: {
        id: '2',
      },
    });

    obj.username = user.username;
    obj.password = this.utils.decodeDZString(user.password);

    obj.fid = event.forum_url_fid;
    obj.tid = event.forum_url;
    obj.digest = param.digest;
    obj.highlight_color = param.highlight_color;
    obj.opt = param.opt;

    const res: any = await this.loginForum(obj);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;

    obj.formhash = variables.formhash;

    let data: any = {
      fid: obj.fid,
      'moderate[]': obj.tid,
      formhash: obj.formhash,
      handlekey: 'mods',
      rdodigest: 'on',
    };

    if (obj.opt === 'highlight') {
      data['operations[]'] = ['highlight'];
      data.highlight_color = obj.highlight_color;
    }

    if (obj.opt === 'digest') {
      data['operations[]'] = ['digest', 'stamp'];

      if (parseInt(obj.digest, 10)) {
        data.digestlevel = 1;
        data.sticklevel = 0;
        data.stamp = 0;
      } else {
        data.digestlevel = 0;
        data.sticklevel = 0;
        data.stamp = '';
      }
    }

    await axios.post(this.discuz.highlightDigest, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookies,
      },
      timeout: 10000,
    });

    let eventModel: any = {};

    if (obj.opt === 'highlight') {
      eventModel.highlight = obj.highlight_color;
    } else if (obj.opt === 'digest') {
      eventModel.digest = obj.digest;
    }

    await this.tblEvent.update({ id: param.eventId }, eventModel);
  }

  async loginForum(user) {
    const result = await axios.post(
      'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=login&loginsubmit=yes&loginfield=auto&submodule=checkpost',
      user,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 10000,
      }
    );

    return result.data;
  }

  async clearWWWCache() {
    const cookie = await this.loginWWW({
      Username: 'Danny',
      Password: '6RW3ME]yp@vi',
    });

    const url = 'https://www.chasedream.com/admin_maintain.aspx';
    let data = {
      ClearCache: true,
    };

    const res = await axios.get(url, {
      headers: {
        Cookie: cookie,
      },
      timeout: 10000,
    });

    const body = res.data.toString();

    let regex =
      /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g;
    let matched = regex.exec(body);
    data['__VIEWSTATE'] = matched[1];

    regex =
      /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g;
    matched = regex.exec(body);
    data['__EVENTVALIDATION'] = matched[1];

    await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookie,
      },
      timeout: 10000,
    });
  }

  async loginWWW(user) {
    const login_url = 'https://www.chasedream.com/Sesame-In.aspx';

    const res = await axios.get(login_url);

    const body = res.data.toString();

    let regex =
      /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g;
    let matched = regex.exec(body);

    let form = {
      __VIEWSTATE: matched[1],
      Submit: true,
    };

    regex =
      /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g;
    matched = regex.exec(body);

    form['__EVENTVALIDATION'] = matched[1];

    const data = Object.assign(form, user);

    const result = await axios.post(login_url, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    const headers = result.headers;
    let iwmsPass = (headers['set-cookie'] && headers['set-cookie'][1]) || '';

    iwmsPass = iwmsPass.substring(
      iwmsPass.indexOf('=') + 1,
      iwmsPass.indexOf(';')
    );

    return `iwmsUser=${user.Username};iwmsPass=${iwmsPass};iwmsAdmin=y;`;
  }

  async publishToWWW(param: any) {
    const obj = {
      classid: 11,
      memberid: 4,
      author: param.username,
      source: 'ChaseDream论坛',
      sourceUrl: 'https://forum.chasedream.com',
      title: encode(param.subject),
      content: param.content,
      allowRemark: false,
    };

    obj.content = this.utils.replaceLinksForPush(obj.content, 'p');

    const news = await this.iwmsNews.save(obj);
    const articleNum = await this.iwmsNews.count({
      where: {
        classid: news.classid,
      },
    });

    await this.iwmsClass.update(
      {
        classID: news.classid,
      },
      {
        articleNum,
      }
    );

    await this.tblEvent.update(
      { id: param.eventId },
      {
        www_url: news.articleid,
        www_url_senduser: param.uid,
      }
    );

    await this.clearWWWCache();
  }

  async updateThread(param) {
    const res = await this.loginForum(param);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    param.formhash = variables.formhash;

    let data: any = {
      delattachop: 0,
      formhash: param.formhash,
      message: param.content,
      subject: param.subject,
      tid: param.tid,
      pid: param.pid,
      fid: param.fid,
      typeid: param.typeid,
      usesig: 1,
      wysiwyg: 1,
      htmlon: param.htmlon,
    };

    if (param.hiddenreplies) {
      data.hiddenreplies = 1;
    }

    if (param.ispid) {
      const post = await this.ccForumPost.findOne({
        where: {
          pid: param.pid,
        },
      });

      if (post.position > 1) delete data.subject;
    }

    const result = await axios.post(
      `${this.discuz.apiBaseUrl}&module=editpost&editsubmit=yes`,
      data,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );

    return result.data;
  }

  async publishToForum(param: any) {
    const thread = await this.ccForumThread.save({
      // @ts-ignore
      fid: param.fid,
      posttableid: 0,
      typeid: param.typeid || 0,
      sortid: 0,
      readperm: 0,
      price: 0,
      author: param.username,
      authorid: param.uid,
      subject: param.subject,
      dateline: this.utils.now(),
      lastpost: this.utils.now(),
      lastposter: param.username,
      views: 0,
      replies: 0,
      displayorder: 0,
      highlight: 0,
      digest: 0,
      rate: 0,
      special: 0,
      attachment: 0,
      moderated: 0,
      closed: 0,
      stickreply: 0,
      recommends: 0,
      recommend_add: 0,
      recommend_sub: 0,
      heats: 0,
      status: 32,
      isgroup: 0,
      favtimes: 0,
      sharetimes: 0,
      stamp: -1,
      icon: -1,
      pushedaid: 0,
      cover: 0,
      replycredit: 0,
      relatebytag: 0,
      maxposition: 1,
      bgcolor: '',
      comments: 0,
      hidden: 0,
      oldtypeid: 0,
      showtime: 0,
    });

    await this.ccForumThreadpartake.save({
      // @ts-ignore
      tid: thread.tid,
      uid: param.uid,
      dateline: this.utils.now(),
    });

    const post = await this.ccForumPostTableid.save({});

    const message = this.utils.replaceLinksForPush(param.content, 'f');

    await this.ccForumPost.save({
      // @ts-ignore
      pid: post.pid,
      fid: param.fid,
      // @ts-ignore
      tid: thread.tid,
      first: 1,
      author: param.username,
      authorid: param.uid,
      subject: '',
      dateline: this.utils.now(),
      message,
      useip: '127.0.0.1',
      port: 0,
      invisible: 0,
      anonymous: 0,
      usesig: 1,
      htmlon: 1,
      bbcodeoff: 0,
      smileyoff: -1,
      parseurloff: 0,
      attachment: 0,
      rate: 0,
      ratetimes: 0,
      status: 0,
      tags: 0,
      comment: 0,
      replycredit: 0,
      replytype: 0,
      position: 1,
    });

    await this.tblEvent.update(
      { id: param.eventId },
      {
        // @ts-ignore
        forum_url: thread.tid,
        // @ts-ignore
        forum_url_fid: thread.fid,
        forum_url_pid: post.pid,
        forum_url_typeid: param.typeid || 0,
        forum_url_senduser: param.uid,
      }
    );

    return thread;
  }

  async nav() {
    const groupList = [38, 3, 1, 97, 2, 4, 45, 65, 5];

    const response = await fetch(
      'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=forumnav',
      {
        method: 'GET',
      }
    );
    const res = await response.json();

    const forums = res.Variables.forums;

    let groups = _.filter(forums, forum => {
      return (
        forum.type === 'group' && groupList.includes(parseInt(forum.fid, 10))
      );
    });

    for (let group of groups) {
      group.forums = _.filter(forums, forum => {
        return forum.type === 'forum' && forum.fup === group.fid;
      });

      delete group.type;
      delete group.fup;
      delete group.status;

      for (let forum of group.forums) {
        delete forum.type;
        delete forum.fup;
        delete forum.status;
        delete forum.viewperm;
        delete forum.postperm;
        delete forum.threadtypes;
      }
    }

    return groups;
  }

  async subnav(param: any) {
    const fid = param.fid;

    const response = await fetch(
      `https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=forumdisplay&tpp=1&fid=${fid}&page=1`,
      {
        method: 'GET',
      }
    );
    const res = await response.json();

    let threadtypes = [];

    if (res.Variables.threadtypes) {
      for (let [key, value] of Object.entries(
        res.Variables.threadtypes.types
      )) {
        threadtypes.push({
          typeid: key,
          // @ts-ignore
          name: value.replace(/(<([^>]+)>)/gi, ''),
        });
      }
    }

    return threadtypes;
  }

  async hasSensitive(str) {
    const all = (await this.commonSensitiveEntity.find()).map(el => el.text);
    let reg = all.join('|');
    const pattern = new RegExp(reg);

    return pattern.test(str);
  }

  async initSearchRecommandIndex() {
    const index = 'search_recommand';

    try {
      await this.es.client.indices.delete({
        index,
      });
    } catch (err) {
      console.log(err.message);
    }

    await this.es.client.indices.create({
      index,
      mappings: {
        properties: {
          id: {
            type: 'long',
          },
          keyword: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          html: {
            type: 'keyword',
          },
          status: {
            type: 'boolean',
          },
        },
      },
    });
  }

  async sensitive(param: any) {
    try {
      const censors = await this.nativeQuery(
        'SELECT * FROM forum.cc_common_word',
        [],
        'forum'
      );

      let banned = [];
      let mod = [];
      let replaced = [];

      let bannednum = 0;
      let modnum = 0;
      let replacednum = 0;

      const censorWords = {
        replaced: [],
        banned: [],
        mod: [],
      };

      for (const censor of censors) {
        if (/^\/(.+?)\/$/.test(censor.find)) {
          switch (censor.replacement) {
            case '{BANNED}':
              censorWords.banned.push(censor.find);
              break;
            case '{MOD}':
              censorWords.mod.push(censor.find);
              break;
            default:
              censorWords.replaced.push(censor.find);
              break;
          }
        } else {
          const findRegex = /\\\{(\d+)\\\}/g;
          const find = censor.find.replace(findRegex, '.{0,$1}');

          switch (censor.replacement) {
            case '{BANNED}':
              banned.push(find);
              bannednum++;
              if (bannednum === 1000) {
                censorWords.banned.push(`${banned.join('|')}`);
                banned = [];
                bannednum = 0;
              }
              break;
            case '{MOD}':
              mod.push(find);
              modnum++;
              if (modnum === 1000) {
                censorWords.mod.push(`${mod.join('|')}`);
                mod = [];
                modnum = 0;
              }
              break;
            default:
              replaced.push(find);
              replacednum++;
              if (replacednum === 1000) {
                censorWords.replaced.push(`${replaced.join('|')}`);
                replaced = [];
                replacednum = 0;
              }
              break;
          }
        }
      }

      if (banned.length) {
        censorWords.banned.push(`${banned.join('|')}`);
      }
      if (mod.length) {
        censorWords.mod.push(`${mod.join('|')}`);
      }
      if (replaced.length) {
        censorWords.replaced.push(`${replaced.join('|')}`);
      }

      return this.censorCheck(censorWords, param.message);
    } catch (err) {
      throw new Error(`请求失败: ${err.message}`);
    }
  }

  async sensitiveMonitorGet(param: any) {
    return await this.baseSysConfService.getValue('forumSensitive');
  }

  async sensitiveMonitorUpdate(param: any) {
    await this.baseSysConfService.updateVaule('forumSensitive', param.val);
  }

  censorCheck(censorWords, message) {
    let found = [];
    const bbcodes =
      'b|i|color|size|font|align|list|indent|email|hide|quote|code|free|table|tr|td|img|swf|attach|payto|float';

    if (censorWords.banned.length) {
      for (const banned_word of censorWords.banned) {
        const regex = new RegExp(banned_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '禁止',
            });
          }
        }
      }
    }

    if (censorWords.mod.length) {
      for (const mod_word of censorWords.mod) {
        const regex = new RegExp(mod_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '审核',
            });
          }
        }
      }
    }

    if (censorWords.replaced.length) {
      for (const replaced_word of censorWords.replaced) {
        const regex = new RegExp(replaced_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '替换',
            });
          }
        }
      }
    }

    return found;
  }

  async initForumIndex() {
    const index = 'forum_post';

    try {
      await this.es.client.indices.delete({
        index,
      });

      await this.baseSysConfService.updateVaule('maxPid', 0);
    } catch (err) {
      console.log(err.message);
    }

    await this.es.client.indices.create({
      index,
      mappings: {
        properties: {
          fid: {
            type: 'long',
          },
          tid: {
            type: 'long',
          },
          subject: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          message: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          lastpost: {
            type: 'long',
          },
          digest: {
            type: 'short',
          },
          displayorder: {
            type: 'short',
          },
          weight: {
            type: 'short',
          },
        },
      },
    });
  }

  async searchRecommendPage(param: any) {
    const { id, gid, name, keyword, content } = param;

    const sql = `
        SELECT a.*, b.name FROM cc_common_search_recommend as a 
        LEFT OUTER JOIN cc_common_search_recommend_group as b 
        on a.gid = b.id 
        WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and a.id=?', id)}
            ${this.setSql(!_.isEmpty(gid), 'and a.gid=?', gid)}
            ${this.setSql(!_.isEmpty(name), 'and b.name like ?', `%${name}%`)}
            ${this.setSql(
              !_.isEmpty(keyword),
              'and a.keyword like ?',
              `%${keyword}%`
            )}
            ${this.setSql(
              !_.isEmpty(content),
              'and a.content like ?',
              `%${content}%`
            )}
            ORDER BY a.id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async searchRecommendAdd(param: any) {
    const index = 'search_recommand';

    const res = await this.ccCommonSearchRecommend.save({
      gid: param.gid,
      keyword: param.keyword,
      content: param.content,
      begin: param.begin || 0,
      end: param.end || 0,
      order: param.order,
      dateline: this.utils.now(),
    });

    await this.es.client.index({
      index,
      id: res.id,
      refresh: true,
      body: {
        content: res.content,
        status: true,
        keyword: param?.keyword
          ?.split('|')
          ?.filter(el => !_.isEmpty(el))
          ?.map(el => decodeURIComponent(el)?.trim()),
      },
    });

    return res;
  }

  async searchRecommendUpdate(param: any) {
    const index = 'search_recommand';

    param.begin = param.begin || 0;
    delete param.name;

    await this.ccCommonSearchRecommend.update(param.id, {
      ...param,
    });

    await this.es.client.update({
      index,
      id: param.id,
      doc: {
        gid: param.gid,
        content: param.content,
        status: !!param.status,
        keyword: param?.keyword
          ?.split('|')
          ?.filter(el => !_.isEmpty(el))
          ?.map(el => decodeURIComponent(el)?.trim()),
      },
    });

    await this.es.client.indices.refresh({ index });
  }

  async searchRecommendDelete(param: any) {
    const index = 'search_recommand';

    await this.ccCommonSearchRecommend.delete({ id: param.id });

    await this.es.client.delete({
      index,
      id: param.id,
    });

    await this.es.client.indices.refresh({ index });
  }

  async searchRecommendGroupList() {
    return await this.ccCommonSearchRecommendGroup.find();
  }

  async searchRecommendGroupPage(param: any) {
    const { id, name } = param;

    const sql = `
        SELECT * FROM cc_common_search_recommend_group WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and id=?', id)}
            ${this.setSql(!_.isEmpty(name), 'and name like ?', `%${name}%`)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async searchRecommendGroupAdd(param: any) {
    return await this.ccCommonSearchRecommendGroup.save({
      name: param.name,
      dateline: this.utils.now(),
    });
  }

  async searchRecommendGroupUpdate(param: any) {
    await this.ccCommonSearchRecommendGroup.update(param.id, {
      ...param,
    });
  }

  async searchRecommendGroupDelete(param: any) {
    await this.ccCommonSearchRecommendGroup.delete({ id: param.id });
  }

  async searchRecommend(kw: string) {
    const index = 'search_recommand';

    let keyword = decodeURIComponent(kw?.trim());
    keyword = keyword.replace(/大学|学院/g, '');

    const res = await this.es.client.search({
      index,
      size: 10,
      from: 0,
      query: {
        bool: {
          must: {
            match: {
              keyword,
            },
          },
          filter: {
            bool: {
              must: [{ term: { status: true } }],
            },
          },
        },
      },
    });

    const ids = res?.hits?.hits.map(el => el._id);

    const result = await this.ccCommonSearchRecommend
      .createQueryBuilder('sr')
      .select(['sr.content', 'sr.order'])
      .where('status=1 and (begin = 0 or begin <= :begin) ', {
        begin: this.utils.now(),
      })
      .andWhere('id in (:ids)', {
        ids: !_.isEmpty(ids) ? ids : [null],
      })
      .orderBy('sr.order', 'ASC')
      .getMany();

    return result.map(el => {
      return {
        order: el.order,
        content: el.content,
      };
    });
  }

  async search(qs) {
    const index = 'forum_post';

    const query: any = {
      function_score: {
        query: {
          bool: {
            must: [
              {
                multi_match: {
                  query: decodeURIComponent(qs?.kw?.trim()),
                  type: 'cross_fields',
                  fields: ['subject^10', 'message'],
                  operator: 'and',
                },
              },
            ],
          },
        },
        field_value_factor: {
          field: 'weight',
        },
      },
    };

    const filter = {
      bool: {
        must: [],
      },
    };

    if (!_.isEmpty(qs.digest)) {
      filter.bool.must.push({
        term: {
          digest: qs.digest,
        },
      });
    }

    if (!_.isEmpty(qs.fid)) {
      filter.bool.must.push({
        terms: {
          fid: qs.fid?.split(','),
        },
      });
    }

    if (!_.isEmpty(qs.lastpost)) {
      const opt = parseInt(qs.before, 10) ? 'lte' : 'gte';
      const time = this.utils.now() - parseInt(qs?.lastpost?.trim(), 10);

      filter.bool.must.push({
        range: {
          lastpost: {
            [opt]: time,
          },
        },
      });
    }

    if (filter.bool.must.length) {
      query.function_score.query.bool.filter = filter;
    }

    const res: any = await this.es.client.search({
      index,
      size: 0,
      query,
      aggs: {
        tids: {
          terms: {
            field: 'tid',
            size: 5000,
          },
          aggs: {
            min_pid: {
              min: {
                field: 'pid',
              },
            },
          },
        },
      },
    });

    const output = {
      tids: [],
      pids: [],
    };

    for (const el of res?.aggregations?.tids?.buckets) {
      output.tids.push(el?.key);
      output.pids.push(parseInt(el?.min_pid?.value));
    }

    return output;
  }

  @CoolCache(5)
  async forumAds4WWW() {
    let cdgs = await this.ccCommonAdvertisement
      .createQueryBuilder('sr')
      .select(['sr.parameters', 'sr.type', 'sr.targets'])
      .where(
        'available = 1 and (starttime = 0 or starttime <= :starttime) and (endtime = 0 or endtime >= :endtime) and type in ("headerbanner","float","footerbanner","cornerbanner","couplebanner") ',
        {
          starttime: this.utils.now(),
          endtime: this.utils.now(),
        }
      )
      .orderBy('sr.displayorder', 'DESC')
      .getMany();

    for (let cdg of cdgs) {
      // @ts-ignore
      cdg.parameters = phpunserialize(cdg.parameters);
    }

    return cdgs;
  }

  async threadWeightPage(param: any) {
    const { tid } = param;

    const sql = `
        SELECT * FROM cc_forum_thread_weight WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(tid), 'and tid=?', tid)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async threadWeight(param: any) {
    return await this.ccForumThreadWeight.findOne({
      where: { tid: param.tid },
    });
  }

  async threadWeightAdd(param: any) {
    const exist = await this.ccForumThreadWeight.findOne({
      where: { tid: param.tid },
    });
    if (exist) {
      await this.ccForumThreadWeight.update(exist.id, {
        weight: param.weight || 0,
      });
    } else {
      await this.ccForumThreadWeight.save({
        tid: param.tid,
        weight: param.weight || 0,
        dateline: this.utils.now(),
      });
    }
  }

  async threadWeightDelete(param: any) {
    await this.ccForumThreadWeight.delete({ tid: param.tid });
  }

  async fixForumPostData() {
    let replyMaxPid =
      (await this.baseSysConfService.getValue('replyMaxPid')) || 0;

    let total = await this.nativeQuery(
      'SELECT count(*) count FROM forum.cc_forum_post where pid > ?',
      [replyMaxPid],
      'forum'
    );

    total = Number(total[0].count);
    let page = 0;
    let pageSize = 1000;

    while (true) {
      const posts = await this.nativeQuery(
        `SELECT pid, tid, message FROM forum.cc_forum_post
              where pid > ?
              order by pid ASC
              limit ${pageSize}`,
        [replyMaxPid],
        'forum'
      );

      try {
        const items = [];
        const regex = /(感谢分享|顶楼主|同意|看一下|Mark一下)！( )*/gi;

        for (const post of posts) {
          try {
            regex.lastIndex = 0;
            let message = post.message?.replace(
              /(\[quote\](.*?)\[\/quote\])|[ \n]/gis,
              ''
            );

            const exist = regex.test(message);

            if (exist && message.length <= 10) {
              const replytype = this.utils.replyType(message);
              items.push({
                replytype,
                pid: post.pid,
              });
            }
          } catch (err) {
            console.log(err.message);
          }
        }

        if (items.length) {
          const replytypes = [1, 2, 3, 4, 5];

          for (const replytype of replytypes) {
            const filtered = items.filter(el => el.replytype === replytype);

            if (filtered.length) {
              await this.ccForumPost
                .createQueryBuilder()
                .update()
                .where('pid in (:pids)', { pids: filtered.map(el => el.pid) })
                .set({ replytype })
                .execute();
            }
          }
        }
      } catch (err) {
        console.log(err.message);
      } finally {
        if (posts?.length) {
          const pids = posts.map(el => el.pid);
          replyMaxPid = Math.max(...pids);

          await this.baseSysConfService.updateVaule('replyMaxPid', replyMaxPid);
        }
      }

      if ((page === 0 ? 1 : page + 1) * pageSize > total) break;
      else page++;
    }
  }
}
