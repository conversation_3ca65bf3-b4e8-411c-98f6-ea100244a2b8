import { Config, Inject, InjectClient, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, In } from 'typeorm';
import { Context } from '@midwayjs/koa';
import { Utils } from '../../../../comm/utils';
import { PortalArticleEntity } from '../../entity/portal/Article';
import { CoolElasticSearch } from '@cool-midway/es';

import * as _ from 'lodash';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';

/**
 * Portal
 */
@Provide()
export class PortalService extends BaseService {
    @Inject()
    ctx: Context;

    @Inject()
    es: CoolElasticSearch;

    @Inject()
    utils: Utils;

    @Config('redisKey')
    redisKey;

    @InjectClient(CachingFactory, 'default')
    midwayCache: MidwayCache;

    @InjectEntityModel(PortalArticleEntity)
    portalArticleEntity: Repository<PortalArticleEntity>;

    async search(params) {
        try {
            const {
                q = '',
                page = 1,
                size = 10,
                tags = '',
                sortBy = 'score'  // 默认按相关性排序
            } = params;

            // 验证分页参数
            const validPage = Math.max(1, parseInt(String(page), 10) || 1);
            const validSize = Math.min(100, Math.max(1, parseInt(String(size), 10) || 10));

            // 构建查询
            const query: any = {
                bool: {
                    must: [],
                    filter: []
                }
            };

            // 添加关键词搜索
            if (q && q.trim()) {
                query.bool.must.push({
                    multi_match: {
                        query: decodeURIComponent(q.trim()),
                        type: 'best_fields',  // 更改为best_fields以获得更好的相关性
                        fields: ['title^10', 'summary^5', 'content'],
                        operator: 'and',                        
                    }
                });
            } else {
                // 如果没有关键词，默认匹配所有文档
                query.bool.must.push({
                    match_all: {}
                });
            }

            // 添加标签过滤
            if (tags && tags.trim()) {
                query.bool.filter.push({
                    term: {
                        tags: tags.trim()
                    }
                });
            }

            // 构建排序
            const sort = [];
            if (sortBy === 'date') {
                sort.push({ datetime: { order: 'desc' } });
            } else if (sortBy === 'score' && q) {
                sort.push({ _score: { order: 'desc' } });
            } else {
                // 默认按日期排序
                sort.push({ datetime: { order: 'desc' } });
            }

            const response: any = await this.es.client.search({
                index: 'portal_articles',
                body: {
                    query,
                    from: (validPage - 1) * validSize,
                    size: validSize,
                    sort,
                    highlight: {
                        fields: {
                            title: { number_of_fragments: 0 },                            
                            summary: { number_of_fragments: 0 }
                        },
                        pre_tags: ['<em>'],
                        post_tags: ['</em>']
                    }
                }
            });

            const hits: any = response.hits.hits;
            const total = response.hits.total.value;

            const results = hits.map(hit => ({
                id: hit._source.id,
                pic: hit._source.pic || '',
                title: hit.highlight?.title?.[0] || hit._source.title,
                summary: hit.highlight?.summary?.[0] || hit.highlight?.content?.[0] || hit._source.summary,
                content: hit._source.content,
                datetime: hit._source.datetime,
                author: hit._source.author || '',
                tags: hit._source.tags || '',
                score: hit._score
            }));

            return {
                list: results,
                pagination: {
                    page: validPage,
                    size: validSize,
                    total
                }
            };
        } catch (error) {
            console.error('搜索出错:', error);
            return {
                list: [],
                pagination: {
                    page: 1,
                    size: 10,
                    total: 0
                },
                error: '搜索服务暂时不可用'
            };
        }
    }

    /**
    * 获取 portalBannerIndex 的值
    */
    async getPortalBannerIndex() {
        const value = await this.midwayCache.get(this.redisKey.portalBannerIndex);
        return value ? parseInt(value as string, 10) : 0;
    }

    /**
     * 设置或更新 portalBannerIndex 的值
     * @param value 要设置的数字值
     */
    async setPortalBannerIndex(value: number) {
        await this.midwayCache.set(this.redisKey.portalBannerIndex, value.toString());
        return value;
    }
}
