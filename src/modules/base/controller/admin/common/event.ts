import { Provide, Post, Inject, Body, ALL } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { EventService } from '../../../service/common/event';
import { TblEvent } from '../../../entity/event/TblEvent';

/**
 * Event
 */
@Provide()
@CoolController({
  api: ['info'],
  entity: TblEvent,
  service: EventService,
})
export class CommonEventController extends BaseController {
  @Inject()
  eventService: EventService;

  @Post('/acList')
  async acList(@Body(ALL) param: any) {
    return this.ok(await this.eventService.acList(param));
  }

  @Post('/acUpdate')
  async acUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.acUpdate(param));
  }

  @Post('/offline')
  async offline(@Body(ALL) param: any) {
    return this.ok(await this.eventService.offline(param));
  }

  @Post('/updateTotalDisplayCount')
  async updateTotalDisplayCount(@Body(ALL) param: any) {
    return this.ok(await this.eventService.updateTotalDisplayCount(param));
  }

  @Post('/updateTotalRedirectCount')
  async updateTotalRedirectCount(@Body(ALL) param: any) {
    return this.ok(await this.eventService.updateTotalRedirectCount(param));
  }

  @Post('/externalsLog')
  async externalsLog(@Body(ALL) param: any) {
    return this.ok(await this.eventService.externalsLog(param));
  }

  @Post('/externals')
  async externals(@Body(ALL) param: any) {
    return this.ok(await this.eventService.externals(param));
  }

  @Post('/publishToApply')
  async publishToApply(@Body(ALL) param: any) {
    return this.ok(await this.eventService.publishToApply(param));
  }

  @Post('/majorCategory')
  async majorCategory(@Body(ALL) param: any) {
    return this.ok(await this.eventService.majorCategory());
  }

  @Post('/schoolCreateAndUpdate')
  async schoolCreateAndUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.schoolCreateAndUpdate(param));
  }

  @Post('/schoolList')
  async schoolList(@Body(ALL) param: any) {
    return this.ok(await this.eventService.schoolList(param));
  }

  @Post('/userDelete')
  async userDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.userDelete(param));
  }

  @Post('/userCreateAndUpdate')
  async userCreateAndUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.userCreateAndUpdate(param));
  }

  @Post('/userList')
  async userList(@Body(ALL) param: any) {
    return this.ok(await this.eventService.userList(param));
  }

  @Post('/changeOrder')
  async changeOrder(@Body(ALL) param: any) {
    return this.ok(await this.eventService.changeOrder(param));
  }

  @Post('/releaseDelete')
  async releaseDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.releaseDelete(param));
  }

  @Post('/push2ComingSoon')
  async push2ComingSoon(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push2ComingSoon(param));
  }

  @Post('/push1Add')
  async push1Add(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push1Add(param));
  }

  @Post('/push1Count')
  async push1Count(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push1Count(param));
  }

  @Post('/push1CountUpdate')
  async push1CountUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push1CountUpdate(param));
  }

  @Post('/wwwHot')
  async wwwHot(@Body(ALL) param: any) {
    return this.ok(await this.eventService.wwwHot(param));
  }

  @Post('/wwwHotUpdate')
  async wwwHotUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.wwwHotUpdate(param));
  }

  @Post('/push1WwwPosition')
  async push1WwwPosition(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push1WwwPosition(param));
  }

  @Post('/push1WwwPositionUpdate')
  async push1WwwPositionUpdate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.push1WwwPositionUpdate(param));
  }

  @Post('/releaseEditOne')
  async releaseEditOne(@Body(ALL) param: any) {
    return this.ok(await this.eventService.releaseEditOne(param));
  }

  @Post('/getReleaseOne')
  async getReleaseOne(@Body(ALL) param: any) {
    return this.ok(await this.eventService.getReleaseOne(param));
  }

  @Post('/releaseList')
  async releaseList(@Body(ALL) param: any) {
    return this.ok(await this.eventService.releaseList(param));
  }

  @Post('/getGeo')
  async getGeo(@Body(ALL) param: any) {
    return this.ok(await this.eventService.getGeo(param));
  }

  @Post('/calendarDelete')
  async calendarDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendarDelete(param));
  }

  @Post('/calendarList')
  async calendarList(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendarList(param));
  }

  @Post('/coreLocationDelete')
  async coreLocationDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.coreLocationDelete(param));
  }

  @Post('/pushLocationDelete')
  async pushLocationDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.pushLocationDelete(param));
  }

  @Post('/release1To3')
  async release1To3(@Body(ALL) param: any) {
    return this.ok(await this.eventService.release1To3(param));
  }

  @Post('/releaseEdit')
  async releaseEdit(@Body(ALL) param: any) {
    return this.ok(await this.eventService.releaseEdit(param));
  }

  @Post('/releaseCreate')
  async releaseCreate(@Body(ALL) param: any) {
    return this.ok(await this.eventService.releaseCreate(param));
  }

  @Post('/calendarEdit')
  async calendarEdit(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendarEdit(param));
  }

  @Post('/calendarEditOne')
  async calendarEditOne(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendarEditOne(param));
  }

  @Post('/getCalendar')
  async getCalendar(@Body(ALL) param: any) {
    return this.ok(await this.eventService.getCalendar(param));
  }

  @Post('/calendarInfo')
  async calendarInfo(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendarInfo(param));
  }

  @Post('/calendar')
  async calendar(@Body(ALL) param: any) {
    return this.ok(await this.eventService.calendar(param));
  }

  @Post('/core')
  async core(@Body(ALL) param: any) {
    return this.ok(await this.eventService.core(param));
  }

  @Post('/remove')
  async remove(@Body(ALL) param: any) {
    return this.ok(await this.eventService.remove(param));
  }

  @Post('/edit')
  async edit(@Body(ALL) param: any) {
    return this.ok(await this.eventService.edit(param));
  }

  @Post('/create')
  async create(@Body(ALL) param: any) {
    return this.ok(await this.eventService.create(param));
  }

  @Post('/imageDelete')
  async imageDelete(@Body(ALL) param: any) {
    return this.ok(await this.eventService.imageDelete(param));
  }

  @Post('/imageDigest')
  async imageDigest(@Body(ALL) param: any) {
    return this.ok(await this.eventService.imageDigest(param));
  }

  @Post('/materialDigest')
  async materialDigest(@Body(ALL) param: any) {
    return this.ok(await this.eventService.materialDigest(param));
  }

  @Post('/school_major')
  async major(@Body(ALL) param: any) {
    return this.ok(await this.eventService.major(param));
  }

  @Post('/type')
  async type(@Body(ALL) param: any) {
    return this.ok(await this.eventService.types(param));
  }

  @Post('/geo')
  async geo(@Body(ALL) param: any) {
    return this.ok(await this.eventService.geo(param));
  }

  @Post('/orgs')
  async orgs(@Body(ALL) param: any) {
    return this.ok(await this.eventService.orgs(param));
  }

  @Post('/urlPage')
  async urlPage(@Body(ALL) param: any) {
    return this.ok(await this.eventService.urlPage(param));
  }
}
