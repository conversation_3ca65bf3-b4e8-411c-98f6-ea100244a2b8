import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';
import { createRedisStore } from '@midwayjs/cache-manager';

/**
 * 本地开发 npm run dev 读取的配置文件
 */
export default {
  serviceUrl:{
    showRelease: 'https://connect.chasedream.com/api/v2/admin/base/open/showRelease',
    wwwCDG: 'https://connect.chasedream.com/api/v2/admin/base/open/wwwCDG',
    idUrl: 'http://localhost:8080',
  },
  noticeType: {
    email: '<EMAIL>',
  },
  rss: {
    email: '<EMAIL>',
  },
  wechaty: {
    puppet: 'wechaty-puppet-padlocal',
    puppetOptions: {
      token: 'puppet_padlocal_a05801ff24de4cb7bc97e48a628e2cdf',
    },
    name: 'wechatBot',
    enable: false,
  },
  cacheManager: {
    clients: {
      default: {
        store: createRedisStore('default'),
      },
    },
  },
  redis: {
    clients: {
      default: {
        host: '**************',
        port: 6379,
        password: 'C7B#cI{jKng5SpZi',
        ttl: null,
        db: 8,
      },
    },
  },
  bull: {
    defaultQueueOptions: {
      redis: {
        host: '**************',
        port: 6379,
        password: 'C7B#cI{jKng5SpZi',
        db: 8,
      },
    },
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '**************',
        port: 3306,
        username: 'root',
        password: 'vmNNO6>FOf2CF*Hi',
        database: 'site_apps_connect',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: true,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        cache: true,
        entities: ['**/modules/*/entity'],
      },
      forum: {
        type: 'mysql',
        host: '**************',
        port: 3306,
        username: 'root',
        password: 'vmNNO6>FOf2CF*Hi',
        database: 'forum',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/forum'],
      },
      chiji: {
        type: 'mysql',
        host: '**************',
        port: 3306,
        username: 'root',
        password: 'vmNNO6>FOf2CF*Hi',
        database: 'site_gmat_chiji',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/chiji'],
      },
      id: {
        type: 'mysql',
        host: '**************',
        port: 3306,
        username: 'root',
        password: 'vmNNO6>FOf2CF*Hi',
        database: 'site_apps_id',
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/event'],
      },
      go: {
        type: 'mssql',
        host: '************',
        port: 3433,
        username: 'conn_GO',
        password: 'Deh9_@#@cqUu#k!S',
        database: 'Site_GO',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/go'],
      },
      www: {
        type: 'mssql',
        host: '************',
        port: 3433,
        username: 'conn_www',
        password: 'QazQMMs89KEBzN6zxgb2d',
        database: 'Site_WWW',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        // 自动建表 注意：线上部署的时候不要使用，有可能导致数据丢失
        synchronize: false,
        // 打印日志
        logging: true,
        // 字符集
        charset: 'utf8mb4',
        entities: ['**/modules/*/entity/www'],
      },
    },
  },
  staticFile: {
    buffer: false,
  },
  cool: {
    // 实体与路径，跟生成代码、前端请求、swagger文档相关 注意：线上不建议开启，以免暴露敏感信息
    eps: true,
    // 是否自动导入模块数据库
    initDB: false,
    // 是否自动导入模块菜单
    initMenu: false,
  } as CoolConfig,
} as MidwayConfig;
